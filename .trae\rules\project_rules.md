此文件配置trae的行为，以满足特定需求。
##总体原则
***综合检索：**所有提问都必须首先检索项目文件夹中的所有相关文档，以获取上下文。
***网络搜索：**检索本地文件后，必须进行网络搜索，以获取更多信息和最新数据。
**回答问题：**在回答问题前请查看相对应的文件，不要自己猜测或者根据惯例回答。
**技能问题：**NPC身上的技能是需要拜师才可以的，当我不提示自己是哪个门派的时候，默认我是百姓根本没有拜师。所以技能获取就不能从NPC身上学习。
使用以下 MCP 模型上下文协议 来增强响应：
**sequential-thinkingmcp:**
*用途：将复杂的问题分解为更小的、更易于管理的部分，并逐步推理以确保答案的逻辑性和连贯性。
*触发条件：当用户提出需要多个步骤或依赖于逻辑推理的问题时使用。这有助于提供更清晰、更有条理的响应。
示例工作流程
1.
用户提出问题。
2.
trae检索项目文件夹中的相关文件。
3.
如果本地没有相关资料，trae在网上搜索相关信息。
4.
使用sequential-thinkingmcp将问题分解为更小的部分，并应用逻辑推理。
5.
trae以清晰且有条理的格式向用户提供最终答案。