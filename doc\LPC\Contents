                             LPC Basics
                      Written by <PERSON><PERSON><PERSON> of Borg
                            23 april 1993

Introduction
Chapter 1: Introduction to the Coding Environment
Chapter 2: The LPC Program
Chapter 3: LPC Data Types
Chapter 4: Functions
Chapter 5: The Basics of Inheritance
Chapter 6: Variable Handling
Chapter 7: Flow Control
Chapter 8: The Data Type Object
