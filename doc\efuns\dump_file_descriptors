dump_file_descriptors - dump the MudOS process file descriptor table

string dump_file_descriptors();

This function is provided to assist in debugging the MudOS driver and
helps overcome deficiencies in some UN*X implementations which do not
provide equivalent or superior debugging facilities as part of the
operating system itself.  The interpretation of the output is very
system-dependent.  Each file descriptor is checked to determine whether
it refers to an open file.  If so, information is displayed from the
"stat structure" returned by the fstat() system call.

The following output was produced on Lambda Realms running on a Sequent
DYNIX/ptx system:

<pre>
Fd  Device Number  Inode   Mode    Uid    Gid      Size
--  -------------  -----  ------  -----  -----  ----------
 0     <USER>      <GROUP>    10319  c  666      0      3           0
 1    79      7   164598  f  644   2862      1      789522
 2    79      7   164598  f  644   2862      1      789522
 3    40    33b     6925  c    0   2862      1           0
 4    40    2a4     6943  c    0   2862      1           0
 5    79      7   164599  f  600   2862      1       44784
 6    40    2e2   145996  c    0   2862      1           0
 7    79      7   164601  f  644   2862      1         506
</pre>

See also:
 dump_socket_status

 Tim <PERSON>@ZorkMUD, Lima Bean, IdeaExchange, and elsewhere
