match_path - search a mapping for a path

mixed match_path( mapping m, string str );

match_path() searches a mapping for a path.  Each key is assumed to be a 
string.  The value is completely arbitrary.  The efun finds the largest 
matching path in the mapping.  Keys ended in '/' are assumed to match paths 
with character that follow the '/', i.e. / is a wildcard for anything below 
this directory.  

 <PERSON>  Beek@ZorkMUD, Lima Bean, IdeaExchange, and elsewhere
