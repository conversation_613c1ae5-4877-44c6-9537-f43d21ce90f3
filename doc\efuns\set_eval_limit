set_eval_limit - set the maximum evaluation cost

void set_eval_limit( int );

set_eval_limit(), with a nonzero argument, sets the maximum evaluation
cost that is allowed for any one thread before a runtime error occurs.
With a zero argument, it sets the current evaluation counter to zero,
and the maximum cost is returned.  set_eval_limit(-1) returns the
remaining evaluation cost.

See also:
 eval_cost

 <PERSON>@ZorkMUD, Lima Bean, IdeaExchange, and elsewhere

