shadow - shadow one or more functions in some object

object shadow( object ob, int flag );

This efun is only available if NO_SHADOWS is not compiled in.

If 'flag' is 1 or missing, then current object will shadow 'ob'. If
'flag' is 0, then either 0 will be returned, or the object that is
already shadowing 'ob'.

The master object defines the function "valid_shadow()".  If it returns 1
the target object can't be shadowed, and the "shadow()" function will
return 0 instead of 'ob'.

If an object 'a' shadows an object 'b', then all "call_other(func)" to 'b'
will be redirected to 'a'. If object 'a' has not defined the function,
then the call will be forwarded to 'b' (as if there were no shadow).
There is only one object that can call functions in 'b' with
call_other(), and that is 'a'. Not even object 'b' can "call_other()"
itself. All normal (internal) function calls inside 'b' will however remain
internal to 'b'.

There are two ways to remove the shadow. Either destruct it, or the object
that was shadowed. In the latter case, the shadow will also be destructed
automatically.  Also, there is a efun remove_shadow() in the contrib package.

The result is that it is possible to hide an object behind another one,
but everything can be totally transparent.  The shadow() efunction makes
it possible to change the behavior of an object without changing the
code for the object in question.  One possible use for shadow() is to
add special capabilities to various classes of players (thief, fighter,
mage, etc).  This usage would make it possible to keep the player object
much simpler than it could be if the code for the various classes had
to be in the player object itself.

See also:
 destruct,
 query_shadowing,
 valid_shadow

 Tim Hollebeek  Beek@ZorkMUD, Lima Bean, IdeaExchange, and elsewhere
