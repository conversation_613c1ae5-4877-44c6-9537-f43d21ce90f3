// client.h
// by augx@sj	12/13/2001

#define CLIENT_FLAG1		254
#define CLIENT_FLAG2		240
#define CLIENT_LOWBYTE_BASE	15
#define CLIENT_LOWBYTE		240
#define CLIENT_TOTAL_BYTES	3839

#define SJCLIENT		"sjclient"
#define NOCLIENT		"dunno"

#define CLIENT_QI		1
#define CLIENT_EXP		2
#define CLIENT_FOOD		3
#define CLIENT_INV_IN		6
#define CLIENT_INV_OUT		7
#define CLIENT_INV_SET		8
#define CLIENT_ROOMOBJ_IN	11
#define CLIENT_ROOMOBJ_OUT	12
#define CLIENT_ROOM		13
#define CLIENT_ROOM_LONG	15

