tell_room - send a message to all objects in a room

void tell_room( mixed ob, string str, object array exclude );

Send a message 'str' to object all objects in the room 'ob'.  'ob' can also 
be the filename of the room (string).  If 'exclude' is specified, all
objects in the exclude array will not receive the message.

See also:
 write,
 shout,
 say,
 tell_object,
 catch_tell

 <PERSON>  Bee<PERSON>@ZorkMUD, Lima Bean, IdeaExchange, and elsewhere
