sort_array - sort an array

array sort_array( array arr, string fun, object ob );

array sort_array( array arr, function f );

array sort_array( array arr, int direction );

The (ob, fun) syntax behaves the same as if (: call_other, ob, fun :)
was passed as f.

In the first two forms, the returned array is sorted with respect to
the comparison function given.  The function takes two elements as
arguments, and returns -1 if if first argument is less than the second,
0 if they are the same, or 1 if the first argument is greater than the
second.

The third form returns an array with the same elements as 'arr', but
quicksorted using built-in sort routines.  A 'direction' of 1 or 0 will
quicksort in ascending order, while a 'direction' of -1 will
quicksort in descending order.  A limitation of the built-in
sort routines is that the array must be homogeneous, composed entirely
of a single type, where that type is string, int, or float.
Arrays of arrays are sorted by sorting based on the first element,
making database sorts possible.

See also:
 strcmp
