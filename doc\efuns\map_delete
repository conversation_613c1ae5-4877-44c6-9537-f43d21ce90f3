map_delete - remove a (key, value) pair from a mapping based on the key

void map_delete( mapping m, mixed element );

map_delete removes the (key, value) from the mapping m that has key equal
to element.

For example, given:

<pre>
mapping names;

names = ([]);
names["truilkan"] = "john";
names["wayfarer"] = "erik";
names["jacques"] = "dwayne";
</pre>

Then:
<pre>
map_delete(names,"truilkan");
</pre>

causes the mapping 'names' to be equal to:
<pre>
(["wayfarer" : "erik", "jacques" : "dwayne"])
</pre>
keys(names) will not contain "truilkan" after map_delete(names,"truilkan")
is called [unless ("truilkan", *) is subsequently added back to the mapping].

See also:
 allocate_mapping

 Tim Hollebeek  Beek@ZorkMUD, Lima Bean, IdeaExchange, and elsewhere
