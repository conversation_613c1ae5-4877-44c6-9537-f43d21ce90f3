swap - swap out a file explicitly

void swap( object );

This efun is only available if the driver is compiled with -DDEBUG.

This efun should be reserved for debugging only.  It
allows an object to be explicitly swapped out.  If
enabled, it is strongly recommended that a simul_efun
override (for this efun) be used to prevent abuse.

Note: objects which have been destructed, already
swapped out, contain a heart beat, cloned, inherited,
or interactive, cannot be swapped out.

 <PERSON>@ZorkMUD, Lima Bean, IdeaExchange, and elsewhere
