author_stats - returns statistics gathered on authors

mapping author_stats( string domain);

This efun is only avaiable if PACKAGE_MUDLIB_STATS is compiled in.

Both domain_stats() and author_stats() return information stored in a
mapping.  If no argument is specified, then information is returned on
all domains (or on all authors) with one map entry per domain or author.
If an argument is specified, then a map is returned that corresponds to
that domain or author with keys: moves, cost, errors, heart_beats,
array_size, and objects.  Each of these map to integer values.
Moves is the number of objects that have moved into objects in the
given domain.  Cost is the number of evaluations (eval_cost) accumulated
by objects with the given domain (or author).  Errors is the number of errors
incurred by objects with the given domain.  Heart_beats is the number of
heartbeat calls made on objects having the given domain.
Array_size is the size (in bytes) of the arrays allocated by the domain.
Objects is the number of objects created by the given domain.  When called
with no arguments, the returned mapping has a form like this:

<pre>
([ domain0 : info0, domain1 : info1, ... ])
</pre>

while info0 has the form:

<pre>
([ "moves" : moves, "cost" : cost, "errors" : errors,
   "heart_beats" : heart_beats, "worth" : worth,
   "array_size" : array_size, "objects" : objects ])
</pre>

When called with an argument, the returned mapping will have the form of
info0.

See also:
 domain_file,
 author_file,
 set_author

 <PERSON> <PERSON>llebeek  Beek@ZorkMUD, Lima Bean, IdeaExchange, and elsewhere

