
     ssssoooocccckkkkeeeetttt____lllliiiisssstttteeeennnn((((3333))))       MMMMuuuuddddOOOOSSSS ((((5555 SSSSeeeepppp 1111999999994444))))        ssssoooocccckkkkeeeetttt____lllliiiisssstttteeeennnn((((3333))))

     NNNNAAAAMMMMEEEE
          socket_listen() - listen for connections on a socket

     SSSSYYYYNNNNOOOOPPPPSSSSIIIISSSS
          #include <socket_err.h>

          int socket_listen( int s, string listen_callback );

     DDDDEEEESSSSCCCCRRRRIIIIPPPPTTTTIIIIOOOONNNN
          To accept connections, a socket is first created with
          socket_create(3), the socket is them put into listening mode
          with socket_listen(3), and the connections are accepted with
          socket_accept(3). The socket_listen() call applies only to
          sockets of type STREAM or MUD.

          The argument listen_callback is the name of a function for
          the driver to call when a connection is requested on the
          listening socket. The listen callback should follow this
          format:

               void listen_callback(int fd)

          Where fd is the listening socket.

     RRRREEEETTTTUUUURRRRNNNN VVVVAAAALLLLUUUUEEEESSSS
          socket_listen() returns:

               EESUCCESS on success.

               a negative value indicated below on error.

     EEEERRRRRRRROOOORRRRSSSS
          EEFDRANGE      Descriptor out of range.

          EEBADF         Descriptor is invalid.

          EESECURITY     Security violation attempted.

          EEMODENOTSUPP  Socket mode not supported.

          EENOADDR       Socket not bound to an address.

          EEISCONN       Socket is already connected.

          EELISTEN       Problem with listen.

     SSSSEEEEEEEE AAAALLLLSSSSOOOO

     Page 1                                          (printed 3/16/95)

     ssssoooocccckkkkeeeetttt____lllliiiisssstttteeeennnn((((3333))))       MMMMuuuuddddOOOOSSSS ((((5555 SSSSeeeepppp 1111999999994444))))        ssssoooocccckkkkeeeetttt____lllliiiisssstttteeeennnn((((3333))))

          socket_accept(3), socket_connect(3), socket_create(3)

     Page 2                                          (printed 3/16/95)

