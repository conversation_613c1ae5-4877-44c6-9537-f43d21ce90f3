restore_object - restore values of variables from a file into an object

int restore_object( string name, int flag );

Restore values of variables for current object from file `name'. If the 
optional second argument is 1, then all of the non-static variables are not 
zeroed out prior to restore (normally, they are).

In the case of an error, the affected variable will be left untouched
and an error given.

See also:
 save_object

 Tim <PERSON>  Beek@ZorkMUD, Lima Bean, IdeaExchange, and elsewhere
