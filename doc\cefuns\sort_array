
     ssssoooorrrrtttt____aaaarrrrrrrraaaayyyy((((3333))))          MMMMuuuuddddOOOOSSSS ((((5555 SSSSeeeepppp 1111999999994444))))           ssssoooorrrrtttt____aaaarrrrrrrraaaayyyy((((3333))))

     NNNNAAAAMMMMEEEE
          sort_array() - sort an array

     SSSSYYYYNNNNOOOOPPPPSSSSIIIISSSS
          mixed *sort_array( mixed *arr, string fun, object ob );
          mixed *sort_array( mixed *arr, function f );
          mixed *sort_array( mixed *arr, int direction );

     DDDDEEEESSSSCCCCRRRRIIIIPPPPTTTTIIIIOOOONNNN
          The first form returns an array with the same elements as
          `arr', but quicksorted in ascending order according to the
          rules in `ob->fun()'.  `ob->fun()' will be passed two
          arguments for each call.  It should return -1, 0, or 1,
          depending on the relationship of the two arguments (lesser,
          equal to, greater than).

          The second form does the same thing but allows a function
          pointer to be used instead.

          The third form returns an array with the same elements as
          'arr', but quicksorted using built-in sort routines.  A
          'direction' of 1 or 0 will quicksort in ascending order,
          while a 'direction' of -1 will quicksort in descending
          order.  A limitation of the built-in sort routines is that
          the array must be homogeneous, composed entirely of a single
          type, where that type is string, int, or float.  Arrays of
          arrays are sorted by sorting based on the first element,
          making database sorts possible.

     SSSSEEEEEEEE AAAALLLLSSSSOOOO
          filter_array(3), map_array(3), strcmp(3)

     Page 1                                          (printed 3/16/95)

