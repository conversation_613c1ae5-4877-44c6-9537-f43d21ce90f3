#ifndef __ARMOR__
#define __ARMOR__

// Standard inheritable armor objects.
#define HEAD		"/inherit/armor/head"
#define NECK		"/inherit/armor/neck"
#define CLOTH		"/inherit/armor/cloth"
#define ARMOR		"/inherit/armor/armor"
#define SURCOAT		"/inherit/armor/surcoat"
#define WAIST		"/inherit/armor/waist"
#define WRISTS		"/inherit/armor/wrists"
#define SHIELD		"/inherit/armor/shield"
#define FINGER		"/inherit/armor/finger"
#define HANDS		"/inherit/armor/hands"
#define BOOTS		"/inherit/armor/boots"
#define BANGLE          "/inherit/armor/bangle"

// Standard armor types
#define TYPE_ARMOR	"armor"
#define TYPE_BOOTS	"boots"
#define TYPE_CLOTH	"cloth"
#define TYPE_COAT	"coat"
#define TYPE_FINGER	"finger"
#define TYPE_HANDS	"hands"
#define TYPE_HEAD	"head"
#define TYPE_NECK	"neck"
#define TYPE_SHIELD	"shield"
#define TYPE_SURCOAT	"surcoat"
#define TYPE_WAIST	"waist"
#define TYPE_WRISTS	"wrists"
#define TYPE_BANGLE     "bangle"
#endif
