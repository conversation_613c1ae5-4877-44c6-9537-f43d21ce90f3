socket_write - send a message from a socket

int socket_write( int s, mixed message,
                    void | string address );

This efun is only available if PACKAGE_SOCKETS is compiled in.

socket_write() sends a message on a socket s. If the socket s is of type
STREAM or MUD, the socket must already be connected and the address is not
specified. If the socket is of type DATAGRAM, the address must be specified.
The address is of the form: "127.0.0.1 23".

socket_write() returns:

EESUCCESS on success.

a negative value indicated below on error.

ERRORS - these are in "socket_err.h"

<DL>
* EEFDRANGE Descriptor out of range.
* EEBADF Descriptor is invalid.
* EESECURITY Security violation attempted.
* EENOADDR Socket not bound to an address.
* EEBADADDR Problem with address format.
* EENOTCONN Socket not connected.
* EEALREADY Operation already in progress.
* EETYPENOTSUPP Object type not supported.
* EEBADDATA Sending data with too many nested levels.
* EESENDTO Problem with sendto.
* EEMODENOTSUPP Socket mode not supported.
* EEWOULDBLOCK Operation would block.
* EESEND Problem with send.
* EECALL<PERSON><PERSON><PERSON> Wait for callback.
</DL>

See also:
 socket_connect,
 socket_create

 <PERSON>@ZorkMUD, Lima Bean, IdeaExchange, and elsewhere
