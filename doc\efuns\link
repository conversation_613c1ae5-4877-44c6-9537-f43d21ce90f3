link - link a file to another

void link( string original, string reference );

Creates a link 'reference' to the file 'original'.  This efun causes
valid_link(original, reference) to be called in the master object.  If
valid_link() returns 0, the link() call fails.  If valid_link() returns 1
then the link() suceeds iff rename() would succeed if called with the same
arguments.

See also:
 rm,
 rmdir,
 rename,
 mkdir,
 cp

 <PERSON>@ZorkMUD, Lima Bean, IdeaExchange, and elsewhere
