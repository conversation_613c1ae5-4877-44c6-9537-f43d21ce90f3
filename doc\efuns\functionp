functionp - determine whether or not a given variable is a function
pointer

int functionp( mixed arg );

Return nonzero if `arg' is a function pointer and zero (0) otherwise.
Function pointers are variables of type 'function' as indicated in the
documentation for the type 'function', for example:

f = (: call_other, obj, func :);

The return value indicates the type of function pointer using the
values given in the driver include file "include/function.h".

<DL>
* FP_LOCAL - lfun pointer
* FP_EFUN - efun pointer
* FP_SIMUL - simul pointer
* FP_FUNCTIONAL - functional
</DL>

These values are bit values; the following flags may be added as well:

<DL>
* FP_HAS_ARGUMENTS - arguments were included in the definition
* FP_OWNER_DESTED - the owner of this function pointer has been destructed
* FP_NOT_BINDABLE - it isn't possible to rebind this function pointer
</DL>

To test if a function variable is an efun pointer:

if (functionp(f) & FP_EFUN) ...

to test if it is an efun or simul_efun:

if (functionp(f) & (FP_EFUN | FP_SIMUL)) ...

Try (very hard) to call the function:

<pre>
if (functionp(f) & FP_OWNER_DESTED) {
   if (functionp(f) & FP_NOT_BINDABLE)
       error("Function could not be rebound.\n");
   f = bind(f, this_object());
}
evaluate(f);
</pre>

 Tim Hollebeek  Beek@ZorkMUD, Lima Bean, IdeaExchange, and elsewhere
