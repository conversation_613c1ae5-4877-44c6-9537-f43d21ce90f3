function_exists - find the file containing a given function in an object

string function_exists( string str, object ob );

Return the file name of the object that defines the function 'str' in
object 'ob'. The returned value can be other than 'file_name(ob)' if the
function is defined by an inherited object.

0 is returned if the function was not defined.

Note that function_exists() does not check shadows, or functions that cannot
be called from outside the object.

See also:
 call_other

 Tim <PERSON>  Bee<PERSON>@ZorkMUD, Lima Bean, IdeaExchange, and elsewhere
