
     ssssoooocccckkkkeeeetttt____aaaaddddddddrrrreeeessssssss((((3333))))      MMMMuuuuddddOOOOSSSS ((((5555 SSSSeeeepppp 1111999999994444))))       ssssoooocccckkkkeeeetttt____aaaaddddddddrrrreeeessssssss((((3333))))

     NNNNAAAAMMMMEEEE
          socket_address() - return the remote address for an efun
          socket

     SSSSYYYYNNNNOOOOPPPPSSSSIIIISSSS
          #include <socket_err.h>

          string socket_address( int s );

     DDDDEEEESSSSCCCCRRRRIIIIPPPPTTTTIIIIOOOONNNN
          socket_address() returns the remote address for an efun
          socket s.  The returned address is of the form:

               "127.0.0.1 23".

     RRRREEEETTTTUUUURRRRNNNN VVVVAAAALLLLUUUUEEEESSSS
          socket_address() returns:

               a string format address on success.

               an empty string on failure.

     SSSSEEEEEEEE AAAALLLLSSSSOOOO
          socket_connect(3), socket_create(3), resolve(3),
          query_host_name(3), query_ip_name(3), query_ip_number(3)

     Page 1                                          (printed 3/16/95)

