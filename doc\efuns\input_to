input_to - causes next line of input to be sent to a specified function

varargs void input_to( string | function fun, int flag, ... );

Enable next line of user input to be sent to the local function 'fun' as
an argument. The input line will not be parsed by the driver.

Note that input_to is non-blocking which means that the object calling
input_to does not pause waiting for input.  Instead the object continues
to execute any statements following the input_to.  The specified function
'fun' will not be called until the user input has been collected.

If "input_to()" is called more than once in the same execution, only the
first call has any effect.

If optional argument 'flag' has the 1 bit set, the line given by the player
will not be echoed, and is not seen if snooped (this is useful for collecting
passwords).

If 'flag' has the 2 bit set, the input_to cannot be bypassed by beginning the
command with '!'.  Otherwise, lines which start with '!' drop through to
the normal input handler.

The function 'fun' will be called with the user input as its first argument
(a string). Any additional arguments supplied to input_to will be passed on to
'fun' as arguments following the user input.

See also:
 call_other,
 call_out,
 get_char

 <PERSON>@ZorkMUD, Lima Bean, IdeaExchange, and elsewhere
