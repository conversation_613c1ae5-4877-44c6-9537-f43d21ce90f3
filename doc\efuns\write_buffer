write_buffer - write a buffer to a file

int write_buffer(mixed dest, int start, mixed source );

If `dest' is a file, then `source' must be an int (and will be written to
the file in network-byte-order), a buffer, or a string, and `source' will
be written to the file `dest' starting at byte # `start'.

If `dest' is a buffer, then `source' will be written into the buffer starting
at byte # `start' in the buffer.  If `source' is an int, it will be written
in network-byte-order.

See also:
 read_buffer,
 allocate_buffer

 Tim <PERSON>  Bee<PERSON>@ZorkMUD, Lima Bean, IdeaExchange, and elsewhere
