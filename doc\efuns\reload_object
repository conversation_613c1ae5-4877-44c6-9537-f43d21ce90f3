reload_object - return an object to its just-loaded state

void reload_object( object ob );

When reload_object() is called on `ob', all the driver-maintained properties
are re-initialized (heart_beat, call_outs, light, shadows, etc), all 
variables are re-initialized, and create() is called.  It has a similar
effect to destructing/reloading the object, however, no disk access or
parsing is performed.

See also:
 new,
 clone_object,
 destruct

 <PERSON>@ZorkMUD, Lima Bean, IdeaExchange, and elsewhere
