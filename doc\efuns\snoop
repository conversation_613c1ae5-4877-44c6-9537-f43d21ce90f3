snoop - snoop an interactive user

varargs object snoop( object snooper, object snoopee );

When both arguments are used, begins snooping of `snoopee' by `snooper'.
If the second argument is omitted, turns off all snooping by `snoopee'.
Security for snoop() is normally controlled by a simul_efun.  snoop() returns
`snoopee' if successful in the two-argument case, and `snooper' if it was
successful in the single-argument case.  A return of 0 indicates failure.

See also:
 query_snoop,
 query_snooping

 Tim <PERSON>  Beek@ZorkMUD, Lima Bean, IdeaExchange, and elsewhere
