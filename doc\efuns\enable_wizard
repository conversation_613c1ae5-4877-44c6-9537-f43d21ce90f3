enable_wizard - give wizard priveleges to an object

void enable_wizard();

Available only if NO_WIZARDS isn't defined.

Any interactive object that calls enable_wizard() will cause wizard<PERSON>()
to return true if called on that object.  enable_wizard() gives three
privileges to the interactive object in question:

<DL>
* ability to use restricted modes of ed when the RESTRICTED_ED option
is compiled into the driver.
*
privilege of receiving descriptive runtime error messages.
*
privilege of using the trace() and traceprefix() efuns.
<DL>

If you don't use this, ed() must be explicitly restricted when necessary,
an error_handler should be implemented to give appropriate messages if you
don't want all users to get descriptive error traces, and trace() and
traceprefix() should be restricted via simul_efuns, if necessary.

See also:
 disable_wizard,
 wizardp

 <PERSON>@ZorkMUD, Lima Bean, IdeaExchange, and elsewhere
