//  File  :  /include/net/daemons.h
//
//  Mudlib Network Daemon Macro defines
#ifndef __NET_DAEMONS_H
#define __NET_DAEMONS_H
 
#define CMWHO_D			"/adm/daemons/network/cmwhod"
#define MAIL_SERVER             "/adm/daemons/network/ms"
#define NAME_SERVER		"/adm/daemons/network/name_server"
#define USERID_D		"/adm/daemons/network/userid"
#define INETD			"/adm/daemons/network/inetd"
#define	PING			"/adm/daemons/network/pingd"
#define TELNET_D		"/adm/daemons/network/telnetd"
#define UDP_MASTER              "/adm/daemons/network/udp_master"
#define INTER_CHAN_D            "/adm/daemons/network/inter_chan"
#define TS_D                    "/adm/daemons/network/ts"
#define DNS_MASTER              "/adm/daemons/network/dns_master"
#define NETMAIL_D		"/adm/daemons/network/netmail"
#define FINGER_SERVER           "/adm/daemons/network/fs"
#define RWHO_D                  "/adm/daemons/network/rwho"

#endif //__NET_DAEMONS_H
