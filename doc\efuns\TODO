query_ip_port(void|object) [PACKAGE_CONTRIB]
mixed query_notify_fail() [!NO_ADD_ACTION] [PACKAGE_CONTRIB]
int remove_interactive(object) [PACKAGE_CONTRIB]
int remove_shadow(object ob) [!NO_SHADOWS] [PACKAGE_CONTRIB]
int replaceable(object, void | string array) [PACKAGE_CONTRIB]
float array rotate_x(float array, float) [PACKAGE_MATRIX]
float array rotate_y(float array, float) [PACKAGE_MATRIX]
float array rotate_z(float array, float) [PACKAGE_MATRIX]
float array scale(float array, float) [PACKAGE_MATRIX]
void set_this_player(object | int) [NO_ADD_ACTION]
void store_variable(string, mixed) [PACKAGE_CONTRIB]
string terminal_colour(string, mapping) [PACKAGE_CONTRIB]
float array translate(float array, float, float, float) [PACKAGE_MATRIX]
mapping unique_mapping(array, string|function, ...)
string upper_case(string) [PACKAGE_CONTRIB]
string array variables(object, int default: 0); [PACKAGE_CONTRIB]
