memory_info - obtain info on object/overall memory usage

varargs int memory_info( object ob );

If optional argument `ob' is given, memory_info() returns the approximate
amount of memory that `ob' is using.  If no argument is given, memory_info()
returns the approximate amount of memory that the entire mud is using.  Note
that the amount of memory the mud is using does not necessarily correspond
to the amount of memory actually allocated by the mud from the system, and
that total memory used by all the objects is not additive due to sharing of
certain structures.

See also:
 debug_info,
 malloc_status,
 mud_status

 Tim <PERSON>k  Beek@ZorkMUD, Lima Bean, IdeaExchange, and elsewhere
