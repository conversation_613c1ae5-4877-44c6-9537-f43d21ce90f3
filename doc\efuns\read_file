read_file - read a file into a string

string read_file( string file, int start_line, int number_of_lines );

Read a line of text from a file into a string.  The second and third
arguments are optional.  If only the first argument is specified, the
entire file is returned (as a string).

The start_line is the line number of the line you wish to read.  This routine
will return 0 if you try to read past the end of the file, or if you try to
read from a nonpositive line.

See also:
 write_file,
 read_buffer
