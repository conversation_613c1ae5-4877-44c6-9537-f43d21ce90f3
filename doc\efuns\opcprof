opcprof - reports statistics on calling frequencies of various efuns

void opcprof();

void opcprof( string );

This function dumps a list of statistics on each efunction and eoperator.
If no argument is specified, then the information will be dumped to files
named /OPCPROF.efun and /OPCPROF.eoper.  If an argument is specified, then
that name is used as the filename for the dump.

See also:
 function_profile

 Tim <PERSON>@ZorkMUD, Lima Bean, IdeaExchange, and elsewhere
