reg_assoc - A regular pattern substring extractor

array reg_assoc(string str, string array pat_arr, array tok_arr);

array reg_assoc(string str, string array pat_arr, array tok_arr, mixed def);

reg_assoc() takes a string and explodes it into substrings matching 
the regular expression pattern strings given in pat_arr and associates
them with tokens given in tok_arr. If def (default 0) is given, it
is associated with a non-match. The return value is an array of
two arrays, the 1st being an array of the form 
<pre>
	({ non-match1, match1, non-match2, match2, ..., 
	   non-match n, match n, non-match n+1 }) 
</pre>
and the 2nd holds the tokens corresponding to the matches in order:
<pre>
	({ def, token corresponding to match1, ...., def, 
	   token corresponding to match n, def }).
</pre>

pat_arr and tok_arr must be of the same sizes, the ith element in 
tok_arr being the corresponding token to the ith element of pat_arr. 
pat_arr can only hold strings. 

If pat_arr (and hence tok_arr) has size 0 then the return value is 
simply ({ ({ str }), ({ def }) }).

EXAMPLE

<pre>
 #define STRING_PAT "\e"(\e\e\e\e.|[^\e\e\e"])*\e""
 #define NUM_PAT "[0\-9]+"

 #define F_STRING 1
 #define F_NUM 2

 reg_assoc("Blah \e"blah\e" test 203 hhh j 308 \e"bacdcd\eb\e"acb",
  ({ STRING_PAT, NUM_PAT }), ({ F_STRING, F_NUM }), "no-match")
</pre>

will return 

<pre>
({ ({ "Blah ", "\e"blah\e"", " test ", "203", " hhh j ", "308", " ",
      "\e"bacdcd\eb\e"", "acb" }),
   ({ "no-match", F_STRING, "no-match", F_NUM, "no-match", F_NUM, 
      "no-match", F_STRING, "no-match" }) })
</pre>

See also:

 regexp [for regular expression syntax]

 Tim Hollebeek  Beek@ZorkMUD, Lima Bean, IdeaExchange, and elsewhere
