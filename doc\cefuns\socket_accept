
     ssssoooocccckkkkeeeetttt____aaaacccccccceeeepppptttt((((3333))))       MMMMuuuuddddOOOOSSSS ((((5555 SSSSeeeepppp 1111999999994444))))        ssssoooocccckkkkeeeetttt____aaaacccccccceeeepppptttt((((3333))))

     NNNNAAAAMMMMEEEE
          socket_accept() - accept a connection on a socket

     SSSSYYYYNNNNOOOOPPPPSSSSIIIISSSS
          #include <socket_err.h>

          int socket_accept( int s, string read_callback,
                             string write_callback );

     DDDDEEEESSSSCCCCRRRRIIIIPPPPTTTTIIIIOOOONNNN
          The argument s is a socket that has been created with
          socket_create(3), bound to an address with socket_bind(3),
          and is listening for connections after a socket_listen(3).
          socket_accept() extracts the first connection on the queue
          of pending connections, creates a new socket with the same
          properties of s and allocates a new file descriptor for the
          socket. If no pending connections are present on the queue,
          socket_accept() returns an error as described below. The
          accepted socket is used to read and write data to and from
          the socket which connected to this one; it is not used to
          accept more connections. The original socket s remains open
          for accepting further connections.

          The argument read_callback is the name of a function for the
          driver to call when the new socket (not the accepting
          socket) receives data.  The write callback should follow
          this format:

               void read_callback(int fd)

          Where fd is the socket which is ready to accept data.

          The argument write_callback is the name of a function for
          the driver to call when the new socket (not the accepting
          socket) is ready to be written to. The write callback should
          follow this format:

               void write_callback(int fd)

          Where fd is the socket which is ready to be written to.

          Note: The close_callback of the accepting socket (not the
          new socket) is called if the new socket closes unexpectedly,
          i.e. not as the result of a socket_close(3) call. The close
          callback should follow this format:

               void close_callback(int fd)

          Where fd is the socket which has closed.

     Page 1                                          (printed 3/16/95)

     ssssoooocccckkkkeeeetttt____aaaacccccccceeeepppptttt((((3333))))       MMMMuuuuddddOOOOSSSS ((((5555 SSSSeeeepppp 1111999999994444))))        ssssoooocccckkkkeeeetttt____aaaacccccccceeeepppptttt((((3333))))

     RRRREEEETTTTUUUURRRRNNNN VVVVAAAALLLLUUUUEEEESSSS
          socket_accept() returns a non-negative descriptor for the
          accepted socket on success. On failure, it returns a
          negative value. socket_error(3) can be used on the return
          value to get a text description of the error.

     EEEERRRRRRRROOOORRRRSSSS
          EEFDRANGE      Descriptor out of range.

          EEBADF         Descriptor is invalid.

          EESECURITY     Security violation attempted.

          EEMODENOTSUPP  Socket mode not supported.

          EENOTLISTN     Socket not listening.

          EEWOULDBLOCK   Operation would block.

          EEINTR         Interrupted system call.

          EEACCEPT       Problem with accept.

          EENOSOCKS      No more available efun sockets.

     SSSSEEEEEEEE AAAALLLLSSSSOOOO
          socket_bind(3), socket_connect(3), socket_create(3),
          socket_listen(3)

     Page 2                                          (printed 3/16/95)

