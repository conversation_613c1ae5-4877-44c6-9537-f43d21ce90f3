seteuid - set the effective user id (euid) of an object

int seteuid( string str );

This efun is only available if PACKAGE_UIDS is compiled in.

Set effective uid to 'str'.  valid_seteuid() in master.c controls which
values the euid of an object may be set to.

When this value is 0, then the current object's uid can be changed by
export_uid(), and only then.

But, when the value is 0, no objects can be loaded or cloned by this object.

See also:
 export_uid,
 getuid,
 geteuid,
 valid_seteuid

 Tim <PERSON>  Beek@ZorkMUD, Lima Bean, IdeaExchange, and elsewhere
