strcmp - determines the lexical relationship between two strings

int strcmp( string one, string two );

This implementatin of strcmp() is identical to the one found in C libraries.
If string one lexically precedes string two, then strcmp() returns a number
less than 0.  If the two strings have the same value, strcmp() returns 0.
If string two lexically precedes string one, then strcmp() returns a number
greater than 0.  This efunction is particularly useful in the compare
functions needed by sort_array().

Note that relational operators (&lt;, &gt;, etc) can also be used to compare
strings.

See also:
 sort_array

 Tim <PERSON>  Bee<PERSON>@ZorkMUD, Lima Bean, IdeaExchange, and elsewhere
