//Cracked by Roath
// stick.c

#include <weapon.h>

#ifdef AS_FEATURE
#include <dbase.h>
#else
inherit EQUIP;
#endif

varargs void init_pike(int damage, int flag)
{
	if( clonep(this_object()) ) return;

	set("weapon_prop/damage", damage);
	set("flag", (int)flag | LONG );
	set("skill_type", "pike");
	if( !query("actions") ) {
		set("actions", (: call_other, WEAPON_D, "query_action" :) );
		set("verbs", ({ "pierce", "thrust", "slam", "impale" }) );
	}
}
mixed hit_ob(object me, object victim, int damage_bonus, int factor)
{
        if (query("poison_applied") > 0){
        victim->apply_condition("snake_poison",
        random(query("poison_applied")/2) + victim->query_condition("snake_poison"));
	if (victim->query("poisoner") != me)
        victim->set("poisoner", me);
	}
}
