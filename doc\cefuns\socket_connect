
     ssssoooocccckkkkeeeetttt____ccccoooonnnnnnnneeeecccctttt((((3333))))      MMMMuuuuddddOOOOSSSS ((((5555 SSSSeeeepppp 1111999999994444))))       ssssoooocccckkkkeeeetttt____ccccoooonnnnnnnneeeecccctttt((((3333))))

     NNNNAAAAMMMMEEEE
          socket_connect() - initiate a connection on a socket

     SSSSYYYYNNNNOOOOPPPPSSSSIIIISSSS
          #include <socket_err.h>

          int socket_connect( int s, string address,
                              string read_callback,
                              string write_callback );

     DDDDEEEESSSSCCCCRRRRIIIIPPPPTTTTIIIIOOOONNNN
          The argument s is a socket. s must be either a STREAM mode
          or a MUD mode socket. address is the address to which the
          socket will attempt to connect.  address is of the form:
          "127.0.0.1 23"

          The argument read_callback is the name of a function for the
          driver to call when the socket gets data from its peer. The
          read callback should follow this format:

               void read_callback(int fd, mixed message)

          Where fd is the socket which received the data, and message
          is the data which was received.

          The argument write_callback is the name of a function for
          the driver to call when the socket is ready to be written
          to. The write callback should follow this format:

               void write_callback(int fd)

          Where fd is the socket which is ready to be written to.

     RRRREEEETTTTUUUURRRRNNNN VVVVAAAALLLLUUUUEEEESSSS
          socket_connect() returns:

               EESUCCESS on success.

               a negative value indicated below on error.

     EEEERRRRRRRROOOORRRRSSSS
          EEFDRANGE      Descriptor out of range.

          EEBADF         Descriptor is invalid.

          EESECURITY     Security violation attempted.

          EEMODENOTSUPP  Socket mode not supported.

     Page 1                                          (printed 3/16/95)

     ssssoooocccckkkkeeeetttt____ccccoooonnnnnnnneeeecccctttt((((3333))))      MMMMuuuuddddOOOOSSSS ((((5555 SSSSeeeepppp 1111999999994444))))       ssssoooocccckkkkeeeetttt____ccccoooonnnnnnnneeeecccctttt((((3333))))

          EEISLISTEN     Socket is listening.

          EEISCONN       Socket is already connected.

          EEBADADDR      Problem with address format.

          EEINTR         Interrupted system call.

          EEADDRINUSE    Address already in use.

          EEALREADY      Operation already in progress.

          EECONNREFUSED  Connection refused.

          EECONNECT      Problem with connect.

     SSSSEEEEEEEE AAAALLLLSSSSOOOO
          socket_accept(3), socket_close(3), socket_create(3)

     Page 2                                          (printed 3/16/95)

