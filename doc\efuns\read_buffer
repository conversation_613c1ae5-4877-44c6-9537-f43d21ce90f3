read_buffer - read from a file and return a buffer

mixed read_buffer( mixed src, int start, int len);

This efun is only available if DISALLOW_BUFFER_TYPE is not compiled in.

If 'src' is a string (filename), then the filename will be read, starting
at byte # 'start', for 'len' bytes, and returned as a buffer.  If neither
argument is given, the entire file is read.

If 'src' is a buffer, then characters are read from the buffer beginning
at byte # 'start' in the buffer, and for 'len' # of bytes, and returned
as a string.

Note that the maximum number of bytes you can read from a file and into
a buffer is controlled via the 'maximum byte transfer' parameter in the
runtime config file.

See also:
 write_buffer,
 allocate_buffer,
 bufferp,
 read_bytes,
 write_bytes

 Tim <PERSON>@ZorkMUD, Lima Bean, IdeaExchange, and elsewhere
